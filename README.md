# 🧩 Electronics Tools Hub - Maketronics Tech Challenge

A comprehensive web application that gathers and displays electronics engineering tools and components data through a simple web interface.

## 🎯 Challenge Objective Met

This project fulfills the **Maketronics Tech Challenge - Smart Data Display** requirements by:
- ✅ **Data Collection**: Gathering 20+ electronics tools and components from web sources
- ✅ **Data Structure**: Organizing data with title, description, category, popularity, and links
- ✅ **Web Interface**: Creating a functional web application to display information
- ✅ **Bonus Features**: Adding search, filters, calculators, and live deployment
- ✅ **Live Hosting**: Deployed on Render for public access

## 🌐 Live Demo

**🔗 Live Application**: https://electronics-tools-hub.onrender.com

## 📊 Data Source & Collection Strategy

**Topic/Category**: Electronics Engineering Tools & Components
**Data Sources**:
- Professional electronics tool databases
- Component specifications and catalogs
- Industry-standard software listings
- Technical documentation and datasheets

**Data Structure** (15-20+ items):
- **Title**: Tool/component name
- **Description**: Detailed functionality and specifications
- **Category**: Domain classification (Analog Design, Digital VLSI, Signal Processing, etc.)
- **Popularity**: Industry adoption level
- **Link**: Direct links to official sources
- **License/Price**: Licensing model or pricing information

## 🔧 Core Features

### Primary Functionality
- **Electronics Tools Database**: 20+ professional tools across 7 engineering domains
- **Component Calculators**: Interactive tools for circuit design calculations
- **Web Interface**: Clean, responsive design for easy navigation and data display
- **Real-time Search**: Instant filtering by domain, license type, and keywords

### Bonus Features ⭐ (Challenge Extras)
- **Search & Filter Function**: Filter by domain, license, popularity
- **Multiple Calculators**:
  - Resistor Color Code Calculator
  - LED Current Calculator
  - Capacitor Code Calculator
  - Voltage Divider Calculator
- **Live Deployment**: Hosted on Render for public access
- **API Endpoints**: RESTful API for data access
- **Responsive Design**: Mobile-friendly interface

## 🚀 Setup Instructions

### Prerequisites
- Python 3.7 or higher
- Git (for cloning)
- Internet connection (for data collection)

### Local Installation & Running

1. **Clone the repository**
   ```bash
   git clone https://github.com/tarunag23/electronics-tools-hub.git
   cd electronics-tools-hub
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Access the application**
   - Open browser to `http://localhost:5000`
   - Or access live version: https://electronics-tools-hub.onrender.com

### Dependencies Used
```
flask==3.0.0
requests==2.31.0
beautifulsoup4==4.12.2
flask-cors==4.0.0
python-dotenv==1.0.0
```

## 📊 Sample Data Output

### Electronics Tools Database (20+ Items)
Each tool entry contains structured data:

```json
{
  "name": "LTspice",
  "description": "Free SPICE circuit simulation software for analog circuits",
  "category": "Analog Circuit Design",
  "popularity": "Industry Standard",
  "license": "Free",
  "link": "https://www.analog.com/ltspice",
  "source": "Analog Devices"
}
```

### Categories Covered:
1. **Analog Circuit Design** (3+ tools): LTspice, Cadence Virtuoso, TINA-TI
2. **Digital VLSI Design** (3+ tools): Synopsys Design Compiler, Cadence Innovus, Yosys
3. **Signal Processing** (3+ tools): MATLAB Signal Processing, GNU Radio, SciPy
4. **Wireless Communication** (3+ tools): Keysight SystemVue, SDR-Radio
5. **Embedded Systems** (4+ tools): Keil MDK, Arduino IDE, PlatformIO, STM32CubeIDE
6. **Mixed Signal Design** (2+ tools): Cadence AMS Designer, Mentor Questa
7. **ML for Electronics** (2+ tools): TensorFlow Lite, Edge Impulse

## 🔌 API Endpoints

- `GET /api/tools` - Get all electronics tools
- `GET /api/tools/search?q=query&category=domain&license=type` - Search and filter tools
- `GET /api/categories` - Get all engineering domains
- `GET /api/refresh` - Refresh tools database

## 🛠️ Technical Stack

- **Backend**: Flask (Python)
- **Data Collection**: BeautifulSoup + Requests
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Data Storage**: JSON files
- **Styling**: Custom CSS with responsive design

## 📱 Screenshots

The application features:
- Modern gradient design with card-based layout
- Real-time search and filtering
- Responsive grid system
- Interactive hover effects
- Clean typography and spacing

## 🔄 Data Sources

**Curated Electronics Engineering Tools** across domains:
1. **Analog Circuit Design**: LTspice, Cadence Virtuoso, TINA-TI
2. **Digital VLSI Design**: Synopsys Design Compiler, Cadence Innovus, Yosys
3. **Mixed Signal Design**: Cadence AMS Designer, Mentor Questa ADMS
4. **Signal Processing**: MATLAB Signal Processing Toolbox, GNU Radio, SciPy Signal
5. **Wireless Communication**: Keysight SystemVue, Wireless InSite, SDR-Radio
6. **Embedded Systems**: Keil MDK, Arduino IDE, PlatformIO, STM32CubeIDE
7. **ML for Electronics**: TensorFlow Lite for Microcontrollers, Edge Impulse, MATLAB Deep Learning Toolbox

## 🎨 Design Decisions

- **Responsive Design**: Mobile-first approach with CSS Grid
- **User Experience**: Intuitive search and filtering
- **Performance**: Efficient data loading and caching
- **Accessibility**: Semantic HTML and keyboard navigation

## 🚀 Deployment Options

### Recommended: Deploy to Render (Free)

1. **Push to GitHub**:
   ```bash
   git init
   git add .
   git commit -m "Electronics Tools Hub"
   git remote add origin https://github.com/YOUR_USERNAME/electronics-tools-hub.git
   git push -u origin main
   ```

2. **Deploy on Render**:
   - Go to [render.com](https://render.com)
   - Connect your GitHub repository
   - Use build command: `pip install -r requirements.txt`
   - Use start command: `python app.py`
   - Deploy!

3. **Your app will be live** at: `https://your-app-name.onrender.com`

### Alternative Options:
- **Vercel**: Serverless deployment (requires modification)
- **GitHub Pages**: Static hosting (frontend only)
- **Railway**: Similar to Render

## 🔮 Future Enhancements

- User favorites and bookmarking
- Tool ratings and reviews
- Advanced filtering options
- Data export functionality
- Real-time notifications for new tools

## � Screenshots & Sample Output

### Main Dashboard
![Dashboard showing electronics tools organized by domain with search functionality]

### Tool Categories
- **Analog Design Tools**: Professional SPICE simulators and circuit design software
- **Digital VLSI Tools**: Synthesis, place & route, and verification tools
- **Signal Processing**: MATLAB toolboxes, GNU Radio, and DSP libraries
- **Embedded Systems**: IDEs, compilers, and development frameworks

### Interactive Calculators
- **Resistor Calculator**: Color band decoder with real-time calculation
- **LED Calculator**: Current limiting resistor calculator
- **Capacitor Calculator**: Numeric code decoder
- **Voltage Divider**: Circuit analysis tool

## 📝 Assumptions Made

1. **Data Accuracy**: Electronics tool information gathered from official sources and documentation
2. **User Knowledge**: Target users have basic electronics engineering background
3. **Browser Compatibility**: Modern browsers with JavaScript enabled
4. **Tool Relevance**: Focus on industry-standard and widely-used professional tools
5. **Data Freshness**: Tool information remains relatively stable over time

## 🎯 Challenge Evaluation Criteria Met

- **✅ Thought Process**: Chose electronics engineering domain for practical utility and professional relevance
- **✅ Data Source Creativity**: Curated professional tools across 7 specialized engineering domains
- **✅ Code Quality**: Clean, readable Flask application with proper structure and documentation
- **✅ Functionality**: Fully working web application with search, filters, and interactive calculators
- **✅ Bonus Features**: Search/filter functions, live deployment, API endpoints, responsive design

## 🔗 Submission Links

- **GitHub Repository**: https://github.com/tarunag23/electronics-tools-hub
- **Live Demo**: https://electronics-tools-hub.onrender.com
- **API Endpoint**: https://electronics-tools-hub.onrender.com/api/tools

## �️ Technical Implementation

- **Language**: Python
- **Backend**: Flask framework
- **Frontend**: HTML, CSS, JavaScript
- **Data Collection**: BeautifulSoup + Requests
- **Deployment**: Render (free tier)
- **Version Control**: Git/GitHub

---
**Built for Maketronics Tech Challenge - Smart Data Display**
*Demonstrating web scraping, data structuring, and web application development for electronics engineering domain*
