# 🛠️ Developer Tools Hub

A smart data collection app that gathers information about popular developer tools and displays them through a clean web interface.

## 🎯 Project Overview

This application solves the challenge of discovering and exploring popular developer tools by:
- Collecting data from GitHub trending repositories and curated tool lists
- Structuring the data with name, description, category, popularity, and links
- Providing a responsive web interface with search and filter capabilities
- Offering API endpoints for programmatic access

## ✨ Features

- **Data Collection**: Automatically scrapes GitHub trending repos and includes curated developer tools
- **Web Interface**: Clean, responsive design with search and category filtering
- **API Endpoints**: RESTful API for accessing tool data programmatically
- **Real-time Search**: Filter tools by name, description, or category
- **Data Refresh**: Update data with a single click
- **Mobile Responsive**: Works seamlessly on desktop and mobile devices

## 🚀 Quick Start

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd developer-tools-hub
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Open your browser**
   Navigate to `http://localhost:5000`

## 📊 Data Structure

Each tool entry contains:
- **Name**: Tool/repository name
- **Description**: Brief description of functionality
- **Category**: Tool category (e.g., "Code Editor", "DevOps", "Frontend Framework")
- **Popularity**: Stars, users, or popularity metric
- **Link**: Direct link to the tool/repository
- **Language**: Primary programming language
- **Source**: Data source (GitHub, official website, etc.)

## 🔌 API Endpoints

- `GET /api/tools` - Get all tools
- `GET /api/tools/search?q=query&category=cat` - Search tools
- `GET /api/categories` - Get all categories
- `GET /api/refresh` - Refresh data from sources

## 🛠️ Technical Stack

- **Backend**: Flask (Python)
- **Data Collection**: BeautifulSoup + Requests
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Data Storage**: JSON files
- **Styling**: Custom CSS with responsive design

## 📱 Screenshots

The application features:
- Modern gradient design with card-based layout
- Real-time search and filtering
- Responsive grid system
- Interactive hover effects
- Clean typography and spacing

## 🔄 Data Sources

1. **GitHub Trending**: Live trending repositories
2. **Curated Lists**: Hand-picked popular developer tools including:
   - Visual Studio Code
   - Docker
   - React
   - Node.js
   - And many more...

## 🎨 Design Decisions

- **Responsive Design**: Mobile-first approach with CSS Grid
- **User Experience**: Intuitive search and filtering
- **Performance**: Efficient data loading and caching
- **Accessibility**: Semantic HTML and keyboard navigation

## 🚀 Deployment Options

The app can be deployed to:
- **Render**: `git push` deployment
- **Vercel**: Serverless deployment
- **Heroku**: Container deployment
- **GitHub Pages**: Static hosting (frontend only)

## 🔮 Future Enhancements

- User favorites and bookmarking
- Tool ratings and reviews
- Advanced filtering options
- Data export functionality
- Real-time notifications for new tools

## 📝 Assumptions Made

- GitHub's trending page structure remains stable
- Tool popularity metrics are meaningful indicators
- Users prefer visual card-based layouts
- Search functionality should be instant and intuitive

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve the application.

## 📄 License

This project is open source and available under the MIT License.
