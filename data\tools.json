{"tools": [{"name": "googleapis/genai-toolbox", "description": "MCP Toolbox for Databases is an open source MCP server for databases.", "category": "GitHub Trending", "popularity": "4,297 stars", "link": "https://github.com/googleapis/genai-toolbox", "language": "Go", "source": "GitHub"}, {"name": "rustfs/rustfs", "description": "🚀 High-performance distributed object storage for MinIO alternative.", "category": "GitHub Trending", "popularity": "3,853 stars", "link": "https://github.com/rustfs/rustfs", "language": "Rust", "source": "GitHub"}, {"name": "anthropics/prompt-eng-interactive-tutorial", "description": "Anthropic's Interactive Prompt Engineering Tutorial", "category": "GitHub Trending", "popularity": "16,255 stars", "link": "https://github.com/anthropics/prompt-eng-interactive-tutorial", "language": "Jupyter Notebook", "source": "GitHub"}, {"name": "Alibaba-NLP/WebAgent", "description": "🌐 WebAgent for Information Seeking bulit by Tongyi Lab: WebWalker & WebDancer & WebSailor https://arxiv.org/pdf/2507.02592", "category": "GitHub Trending", "popularity": "2,999 stars", "link": "https://github.com/Alibaba-NLP/WebAgent", "language": "Python", "source": "GitHub"}, {"name": "putyy/res-downloader", "description": "视频号、小程序、抖音、快手、小红书、直播流、m3u8、酷狗、QQ音乐等常见网络资源下载!", "category": "GitHub Trending", "popularity": "8,436 stars", "link": "https://github.com/putyy/res-downloader", "language": "Go", "source": "GitHub"}, {"name": "ed-donner/agents", "description": "Repo for the Complete Agentic AI Engineering Course", "category": "GitHub Trending", "popularity": "1,133 stars", "link": "https://github.com/ed-donner/agents", "language": "Jupyter Notebook", "source": "GitHub"}, {"name": "wanghongenpin/proxypin", "description": "Open source free capture HTTP(S) traffic software ProxyPin, supporting full platform systems", "category": "GitHub Trending", "popularity": "9,485 stars", "link": "https://github.com/wanghongenpin/proxypin", "language": "Dart", "source": "GitHub"}, {"name": "microsoft/ai-agents-for-beginners", "description": "11 Lessons to Get Started Building AI Agents", "category": "GitHub Trending", "popularity": "29,730 stars", "link": "https://github.com/microsoft/ai-agents-for-beginners", "language": "Jupyter Notebook", "source": "GitHub"}, {"name": "Visual Studio Code", "description": "Free source-code editor with debugging, task running, and version control", "category": "Code Editor", "popularity": "150k+ stars", "link": "https://code.visualstudio.com/", "language": "TypeScript", "source": "Microsoft"}, {"name": "<PERSON>er", "description": "Platform for developing, shipping, and running applications in containers", "category": "DevOps", "popularity": "65k+ stars", "link": "https://www.docker.com/", "language": "Go", "source": "Docker Inc"}, {"name": "Postman", "description": "API platform for building and using APIs", "category": "API Testing", "popularity": "25M+ users", "link": "https://www.postman.com/", "language": "JavaScript", "source": "Postman"}, {"name": "Git", "description": "Distributed version control system for tracking changes in source code", "category": "Version Control", "popularity": "45k+ stars", "link": "https://git-scm.com/", "language": "C", "source": "Git Community"}, {"name": "Node.js", "description": "JavaScript runtime built on Chrome's V8 JavaScript engine", "category": "Runtime", "popularity": "95k+ stars", "link": "https://nodejs.org/", "language": "JavaScript", "source": "Node.js Foundation"}, {"name": "React", "description": "JavaScript library for building user interfaces", "category": "Frontend Framework", "popularity": "200k+ stars", "link": "https://reactjs.org/", "language": "JavaScript", "source": "Meta"}, {"name": "Python", "description": "High-level programming language for general-purpose programming", "category": "Programming Language", "popularity": "50k+ stars", "link": "https://www.python.org/", "language": "Python", "source": "Python Software Foundation"}, {"name": "Figma", "description": "Web-based UI and UX design application", "category": "Design Tool", "popularity": "10M+ users", "link": "https://www.figma.com/", "language": "TypeScript", "source": "Figma"}, {"name": "<PERSON><PERSON>ck", "description": "Business communication platform for team collaboration", "category": "Communication", "popularity": "12M+ users", "link": "https://slack.com/", "language": "JavaScript", "source": "Slack Technologies"}, {"name": "Notion", "description": "All-in-one workspace for notes, tasks, wikis, and databases", "category": "Productivity", "popularity": "20M+ users", "link": "https://www.notion.so/", "language": "TypeScript", "source": "Notion Labs"}], "last_updated": "2025-07-10T12:19:48.262875", "total_count": 18}