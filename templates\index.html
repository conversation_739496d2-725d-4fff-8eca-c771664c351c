<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electronics Engineering Tools Hub ⚡</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box, .category-select {
            flex: 1;
            min-width: 200px;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .search-box:focus, .category-select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 600;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .stats {
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }

        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .tool-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border-left: 5px solid #667eea;
        }

        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .tool-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .tool-name {
            font-size: 1.4rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .tool-category {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .tool-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .tool-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #888;
        }

        .tool-link {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            transition: background 0.3s;
        }

        .tool-link:hover {
            background: #5a6fd8;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: white;
            font-size: 1.2rem;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #dc3545;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .search-box, .category-select {
                min-width: 100%;
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Electronics Engineering Tools Hub</h1>
            <p>Discover professional tools for analog design, digital VLSI, signal processing, wireless communication, embedded systems & more</p>
        </div>

        <div class="controls">
            <input type="text" id="searchBox" class="search-box" placeholder="Search electronics tools...">
            <select id="categorySelect" class="category-select">
                <option value="">All Domains</option>
            </select>
            <select id="licenseSelect" class="category-select">
                <option value="">All Licenses</option>
                <option value="Free">Free</option>
                <option value="Open Source">Open Source</option>
                <option value="Commercial">Commercial</option>
                <option value="Freemium">Freemium</option>
            </select>
            <button id="searchBtn" class="btn btn-primary">Search</button>
            <button id="refreshBtn" class="btn btn-secondary">Refresh Data</button>
        </div>

        <div id="stats" class="stats" style="display: none;">
            <strong id="toolCount">0</strong> tools found
        </div>

        <div id="error" class="error" style="display: none;"></div>
        <div id="loading" class="loading">Loading tools...</div>
        <div id="toolsGrid" class="tools-grid"></div>
    </div>

    <script>
        class ToolsApp {
            constructor() {
                this.tools = [];
                this.categories = [];
                this.init();
            }

            async init() {
                await this.loadCategories();
                await this.loadTools();
                this.setupEventListeners();
            }

            async loadTools(query = '', category = '', license = '') {
                try {
                    this.showLoading(true);
                    this.hideError();

                    let url = '/api/tools';
                    if (query || category || license) {
                        const params = new URLSearchParams();
                        if (query) params.append('q', query);
                        if (category) params.append('category', category);
                        if (license) params.append('license', license);
                        url = `/api/tools/search?${params}`;
                    }

                    const response = await fetch(url);
                    const data = await response.json();

                    if (response.ok) {
                        this.tools = data.tools || data.tools || [];
                        this.displayTools();
                        this.updateStats();
                    } else {
                        this.showError(data.error || 'Failed to load tools');
                    }
                } catch (error) {
                    this.showError('Network error: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            async loadCategories() {
                try {
                    const response = await fetch('/api/categories');
                    const data = await response.json();
                    
                    if (response.ok) {
                        this.categories = data.categories || [];
                        this.populateCategories();
                    }
                } catch (error) {
                    console.error('Failed to load categories:', error);
                }
            }

            populateCategories() {
                const select = document.getElementById('categorySelect');
                this.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    select.appendChild(option);
                });
            }

            displayTools() {
                const grid = document.getElementById('toolsGrid');
                
                if (this.tools.length === 0) {
                    grid.innerHTML = '<div style="text-align: center; color: white; font-size: 1.2rem; grid-column: 1/-1;">No tools found matching your criteria.</div>';
                    return;
                }

                grid.innerHTML = this.tools.map(tool => `
                    <div class="tool-card">
                        <div class="tool-header">
                            <div>
                                <div class="tool-name">${this.escapeHtml(tool.name)}</div>
                            </div>
                            <div class="tool-category">${this.escapeHtml(tool.category)}</div>
                        </div>
                        <div class="tool-description">${this.escapeHtml(tool.description)}</div>
                        <div class="tool-meta">
                            <span>⭐ ${this.escapeHtml(tool.popularity)}</span>
                            <span>📄 ${this.escapeHtml(tool.license || 'N/A')}</span>
                        </div>
                        <a href="${this.escapeHtml(tool.link)}" target="_blank" class="tool-link">
                            Visit Tool →
                        </a>
                    </div>
                `).join('');
            }

            updateStats() {
                const stats = document.getElementById('stats');
                const count = document.getElementById('toolCount');
                count.textContent = this.tools.length;
                stats.style.display = 'block';
            }

            async refreshData() {
                try {
                    this.showLoading(true);
                    this.hideError();
                    
                    const response = await fetch('/api/refresh');
                    const data = await response.json();
                    
                    if (response.ok) {
                        await this.loadCategories();
                        await this.loadTools();
                        alert('Data refreshed successfully!');
                    } else {
                        this.showError(data.error || 'Failed to refresh data');
                    }
                } catch (error) {
                    this.showError('Network error: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            setupEventListeners() {
                const searchBtn = document.getElementById('searchBtn');
                const refreshBtn = document.getElementById('refreshBtn');
                const searchBox = document.getElementById('searchBox');
                const categorySelect = document.getElementById('categorySelect');

                const licenseSelect = document.getElementById('licenseSelect');

                searchBtn.addEventListener('click', () => {
                    const query = searchBox.value.trim();
                    const category = categorySelect.value;
                    const license = licenseSelect.value;
                    this.loadTools(query, category, license);
                });

                searchBox.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        searchBtn.click();
                    }
                });

                categorySelect.addEventListener('change', () => {
                    const query = searchBox.value.trim();
                    const category = categorySelect.value;
                    const license = licenseSelect.value;
                    this.loadTools(query, category, license);
                });

                licenseSelect.addEventListener('change', () => {
                    const query = searchBox.value.trim();
                    const category = categorySelect.value;
                    const license = licenseSelect.value;
                    this.loadTools(query, category, license);
                });

                refreshBtn.addEventListener('click', () => {
                    this.refreshData();
                });
            }

            showLoading(show) {
                const loading = document.getElementById('loading');
                loading.style.display = show ? 'block' : 'none';
            }

            showError(message) {
                const error = document.getElementById('error');
                error.textContent = message;
                error.style.display = 'block';
            }

            hideError() {
                const error = document.getElementById('error');
                error.style.display = 'none';
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // Initialize the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new ToolsApp();
        });
    </script>
</body>
</html>
