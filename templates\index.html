<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electronics Engineering Tools Hub ⚡</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background-color: white;
            padding: 20px;
            border: 2px solid #333;
            border-radius: 10px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1rem;
        }

        .controls {
            background: white;
            padding: 15px;
            border: 2px solid #333;
            margin-bottom: 20px;
        }

        .search-box, .category-select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            font-size: 14px;
            width: 200px;
        }

        .btn {
            padding: 8px 15px;
            margin: 5px;
            border: 1px solid #333;
            background-color: #ddd;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background-color: #bbb;
        }

        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }

        .btn-secondary {
            background-color: #f44336;
            color: white;
        }

        .stats {
            background: white;
            padding: 10px;
            border: 1px solid #ccc;
            margin-bottom: 15px;
            text-align: center;
        }

        .tools-grid {
            display: block;
        }

        .tool-card {
            background: white;
            border: 2px solid #333;
            padding: 15px;
            margin-bottom: 15px;
        }

        .tool-header {
            margin-bottom: 10px;
        }

        .tool-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .tool-category {
            background: #ffeb3b;
            color: #333;
            padding: 3px 8px;
            border: 1px solid #333;
            font-size: 0.8rem;
            display: inline-block;
        }

        .tool-description {
            color: #555;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .tool-meta {
            margin-bottom: 10px;
            font-size: 0.9rem;
            color: #777;
        }

        .tool-link {
            background: #2196F3;
            color: white;
            padding: 5px 10px;
            text-decoration: none;
            border: 1px solid #333;
        }

        .tool-link:hover {
            background: #1976D2;
        }

        .loading {
            text-align: center;
            padding: 20px;
            background: white;
            border: 1px solid #ccc;
        }

        .error {
            background: #ffcdd2;
            color: #d32f2f;
            padding: 10px;
            border: 1px solid #f44336;
            margin-bottom: 15px;
        }

        /* Simple responsive design */
        @media (max-width: 600px) {
            .search-box, .category-select {
                width: 100%;
                margin: 2px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Electronics Engineering Tools Hub</h1>
            <p>Find tools for electronics engineers - analog, digital, VLSI, signal processing, wireless, embedded, PCB design & more!</p>
            <marquee scrollamount="3" style="color: red; margin-top: 10px;">Welcome to my electronics tools website! Made by an electronics engineering student.</marquee>
        </div>

        <div class="controls">
            <input type="text" id="searchBox" class="search-box" placeholder="Search electronics tools...">
            <select id="categorySelect" class="category-select">
                <option value="">All Domains</option>
            </select>
            <select id="licenseSelect" class="category-select">
                <option value="">All Licenses</option>
                <option value="Free">Free</option>
                <option value="Open Source">Open Source</option>
                <option value="Commercial">Commercial</option>
                <option value="Freemium">Freemium</option>
            </select>
            <button id="searchBtn" class="btn btn-primary">Search</button>
            <button id="refreshBtn" class="btn btn-secondary">Refresh Data</button>
        </div>

        <div id="stats" class="stats" style="display: none;">
            <b>Results:</b> <span id="toolCount">0</span> tools found
        </div>

        <div id="error" class="error" style="display: none;"></div>
        <div id="loading" class="loading">Loading tools...</div>
        <div id="toolsGrid" class="tools-grid"></div>
    </div>

    <script>
        class ToolsApp {
            constructor() {
                this.tools = [];
                this.categories = [];
                this.init();
            }

            async init() {
                await this.loadCategories();
                await this.loadTools();
                this.setupEventListeners();
            }

            async loadTools(query = '', category = '', license = '') {
                try {
                    this.showLoading(true);
                    this.hideError();

                    let url = '/api/tools';
                    if (query || category || license) {
                        const params = new URLSearchParams();
                        if (query) params.append('q', query);
                        if (category) params.append('category', category);
                        if (license) params.append('license', license);
                        url = `/api/tools/search?${params}`;
                    }

                    const response = await fetch(url);
                    const data = await response.json();

                    if (response.ok) {
                        this.tools = data.tools || data.tools || [];
                        this.displayTools();
                        this.updateStats();
                    } else {
                        this.showError(data.error || 'Failed to load tools');
                    }
                } catch (error) {
                    this.showError('Network error: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            async loadCategories() {
                try {
                    const response = await fetch('/api/categories');
                    const data = await response.json();
                    
                    if (response.ok) {
                        this.categories = data.categories || [];
                        this.populateCategories();
                    }
                } catch (error) {
                    console.error('Failed to load categories:', error);
                }
            }

            populateCategories() {
                const select = document.getElementById('categorySelect');
                this.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    select.appendChild(option);
                });
            }

            displayTools() {
                const grid = document.getElementById('toolsGrid');
                
                if (this.tools.length === 0) {
                    grid.innerHTML = '<div style="text-align: center; color: white; font-size: 1.2rem; grid-column: 1/-1;">No tools found matching your criteria.</div>';
                    return;
                }

                grid.innerHTML = this.tools.map(tool => `
                    <div class="tool-card">
                        <div class="tool-header">
                            <div>
                                <div class="tool-name">${this.escapeHtml(tool.name)}</div>
                            </div>
                            <div class="tool-category">${this.escapeHtml(tool.category)}</div>
                        </div>
                        <div class="tool-description">${this.escapeHtml(tool.description)}</div>
                        <div class="tool-meta">
                            <span>⭐ ${this.escapeHtml(tool.popularity)}</span>
                            <span>📄 ${this.escapeHtml(tool.license || 'N/A')}</span>
                        </div>
                        <a href="${this.escapeHtml(tool.link)}" target="_blank" class="tool-link">
                            Visit Tool →
                        </a>
                    </div>
                `).join('');
            }

            updateStats() {
                const stats = document.getElementById('stats');
                const count = document.getElementById('toolCount');
                count.textContent = this.tools.length;
                stats.style.display = 'block';
            }

            async refreshData() {
                try {
                    this.showLoading(true);
                    this.hideError();
                    
                    const response = await fetch('/api/refresh');
                    const data = await response.json();
                    
                    if (response.ok) {
                        await this.loadCategories();
                        await this.loadTools();
                        alert('Data refreshed successfully!');
                    } else {
                        this.showError(data.error || 'Failed to refresh data');
                    }
                } catch (error) {
                    this.showError('Network error: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            setupEventListeners() {
                const searchBtn = document.getElementById('searchBtn');
                const refreshBtn = document.getElementById('refreshBtn');
                const searchBox = document.getElementById('searchBox');
                const categorySelect = document.getElementById('categorySelect');

                const licenseSelect = document.getElementById('licenseSelect');

                searchBtn.addEventListener('click', () => {
                    const query = searchBox.value.trim();
                    const category = categorySelect.value;
                    const license = licenseSelect.value;
                    this.loadTools(query, category, license);
                });

                searchBox.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        searchBtn.click();
                    }
                });

                categorySelect.addEventListener('change', () => {
                    const query = searchBox.value.trim();
                    const category = categorySelect.value;
                    const license = licenseSelect.value;
                    this.loadTools(query, category, license);
                });

                licenseSelect.addEventListener('change', () => {
                    const query = searchBox.value.trim();
                    const category = categorySelect.value;
                    const license = licenseSelect.value;
                    this.loadTools(query, category, license);
                });

                refreshBtn.addEventListener('click', () => {
                    this.refreshData();
                });
            }

            showLoading(show) {
                const loading = document.getElementById('loading');
                loading.style.display = show ? 'block' : 'none';
            }

            showError(message) {
                const error = document.getElementById('error');
                error.textContent = message;
                error.style.display = 'block';
            }

            hideError() {
                const error = document.getElementById('error');
                error.style.display = 'none';
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // Initialize the app when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new ToolsApp();
        });
    </script>
</body>
</html>
