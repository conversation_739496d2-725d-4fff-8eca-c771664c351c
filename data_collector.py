import requests
from bs4 import BeautifulSoup
import json
import time
from datetime import datetime
import os

class DataCollector:
    def __init__(self):
        self.tools_data = []
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    def collect_github_trending(self):
        """Collect trending repositories from GitHub"""
        print("Collecting GitHub trending repositories...")
        
        try:
            url = "https://github.com/trending"
            response = requests.get(url, headers=self.headers)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            repos = soup.find_all('article', class_='Box-row')
            
            for repo in repos[:8]:  # Get top 8 trending repos
                try:
                    # Extract repository name and link
                    title_elem = repo.find('h2', class_='h3')
                    if not title_elem:
                        continue
                    
                    link_elem = title_elem.find('a')
                    if not link_elem:
                        continue
                    
                    name = link_elem.get_text().strip().replace('\n', '').replace(' ', '')
                    link = f"https://github.com{link_elem.get('href')}"
                    
                    # Extract description
                    desc_elem = repo.find('p', class_='col-9')
                    description = desc_elem.get_text().strip() if desc_elem else "No description available"
                    
                    # Extract stars
                    stars_elem = repo.find('a', href=lambda x: x and '/stargazers' in x)
                    stars = stars_elem.get_text().strip() if stars_elem else "0"
                    
                    # Extract language
                    lang_elem = repo.find('span', {'itemprop': 'programmingLanguage'})
                    language = lang_elem.get_text().strip() if lang_elem else "Unknown"
                    
                    self.tools_data.append({
                        'name': name,
                        'description': description,
                        'category': 'GitHub Trending',
                        'popularity': f"{stars} stars",
                        'link': link,
                        'language': language,
                        'source': 'GitHub'
                    })
                    
                except Exception as e:
                    print(f"Error processing repo: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error collecting GitHub trending: {e}")
    
    def collect_awesome_lists(self):
        """Collect popular tools from curated awesome lists"""
        print("Collecting tools from awesome lists...")
        
        # Manually curated list of popular developer tools
        awesome_tools = [
            {
                'name': 'Visual Studio Code',
                'description': 'Free source-code editor with debugging, task running, and version control',
                'category': 'Code Editor',
                'popularity': '150k+ stars',
                'link': 'https://code.visualstudio.com/',
                'language': 'TypeScript',
                'source': 'Microsoft'
            },
            {
                'name': 'Docker',
                'description': 'Platform for developing, shipping, and running applications in containers',
                'category': 'DevOps',
                'popularity': '65k+ stars',
                'link': 'https://www.docker.com/',
                'language': 'Go',
                'source': 'Docker Inc'
            },
            {
                'name': 'Postman',
                'description': 'API platform for building and using APIs',
                'category': 'API Testing',
                'popularity': '25M+ users',
                'link': 'https://www.postman.com/',
                'language': 'JavaScript',
                'source': 'Postman'
            },
            {
                'name': 'Git',
                'description': 'Distributed version control system for tracking changes in source code',
                'category': 'Version Control',
                'popularity': '45k+ stars',
                'link': 'https://git-scm.com/',
                'language': 'C',
                'source': 'Git Community'
            },
            {
                'name': 'Node.js',
                'description': 'JavaScript runtime built on Chrome\'s V8 JavaScript engine',
                'category': 'Runtime',
                'popularity': '95k+ stars',
                'link': 'https://nodejs.org/',
                'language': 'JavaScript',
                'source': 'Node.js Foundation'
            },
            {
                'name': 'React',
                'description': 'JavaScript library for building user interfaces',
                'category': 'Frontend Framework',
                'popularity': '200k+ stars',
                'link': 'https://reactjs.org/',
                'language': 'JavaScript',
                'source': 'Meta'
            },
            {
                'name': 'Python',
                'description': 'High-level programming language for general-purpose programming',
                'category': 'Programming Language',
                'popularity': '50k+ stars',
                'link': 'https://www.python.org/',
                'language': 'Python',
                'source': 'Python Software Foundation'
            },
            {
                'name': 'Figma',
                'description': 'Web-based UI and UX design application',
                'category': 'Design Tool',
                'popularity': '10M+ users',
                'link': 'https://www.figma.com/',
                'language': 'TypeScript',
                'source': 'Figma'
            },
            {
                'name': 'Slack',
                'description': 'Business communication platform for team collaboration',
                'category': 'Communication',
                'popularity': '12M+ users',
                'link': 'https://slack.com/',
                'language': 'JavaScript',
                'source': 'Slack Technologies'
            },
            {
                'name': 'Notion',
                'description': 'All-in-one workspace for notes, tasks, wikis, and databases',
                'category': 'Productivity',
                'popularity': '20M+ users',
                'link': 'https://www.notion.so/',
                'language': 'TypeScript',
                'source': 'Notion Labs'
            }
        ]
        
        self.tools_data.extend(awesome_tools)
    
    def collect_all_data(self):
        """Collect data from all sources"""
        print("Starting data collection...")
        self.tools_data = []
        
        # Collect from different sources
        self.collect_github_trending()
        time.sleep(2)  # Be respectful to APIs
        self.collect_awesome_lists()
        
        # Save to JSON file
        data = {
            'tools': self.tools_data,
            'last_updated': datetime.now().isoformat(),
            'total_count': len(self.tools_data)
        }
        
        os.makedirs('data', exist_ok=True)
        with open('data/tools.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"Collected {len(self.tools_data)} tools successfully!")
        return data

if __name__ == "__main__":
    collector = DataCollector()
    collector.collect_all_data()
