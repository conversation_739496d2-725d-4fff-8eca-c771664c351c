import requests
from bs4 import BeautifulSoup
import json
import time
from datetime import datetime
import os

class ElectronicsToolsCollector:
    def __init__(self):
        self.tools_data = []
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    def collect_github_trending(self):
        """Collect trending repositories from GitHub"""
        print("Collecting GitHub trending repositories...")
        
        try:
            url = "https://github.com/trending"
            response = requests.get(url, headers=self.headers)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            repos = soup.find_all('article', class_='Box-row')
            
            for repo in repos[:8]:  # Get top 8 trending repos
                try:
                    # Extract repository name and link
                    title_elem = repo.find('h2', class_='h3')
                    if not title_elem:
                        continue
                    
                    link_elem = title_elem.find('a')
                    if not link_elem:
                        continue
                    
                    name = link_elem.get_text().strip().replace('\n', '').replace(' ', '')
                    link = f"https://github.com{link_elem.get('href')}"
                    
                    # Extract description
                    desc_elem = repo.find('p', class_='col-9')
                    description = desc_elem.get_text().strip() if desc_elem else "No description available"
                    
                    # Extract stars
                    stars_elem = repo.find('a', href=lambda x: x and '/stargazers' in x)
                    stars = stars_elem.get_text().strip() if stars_elem else "0"
                    
                    # Extract language
                    lang_elem = repo.find('span', {'itemprop': 'programmingLanguage'})
                    language = lang_elem.get_text().strip() if lang_elem else "Unknown"
                    
                    self.tools_data.append({
                        'name': name,
                        'description': description,
                        'category': 'GitHub Trending',
                        'popularity': f"{stars} stars",
                        'link': link,
                        'language': language,
                        'source': 'GitHub'
                    })
                    
                except Exception as e:
                    print(f"Error processing repo: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error collecting GitHub trending: {e}")
    
    def collect_electronics_tools(self):
        """Collect curated electronics engineering tools by domain"""
        print("Collecting electronics engineering tools...")

        # Comprehensive list of electronics engineering tools by domain
        electronics_tools = [
            # Analog Circuit Design
            {
                'name': 'LTspice',
                'description': 'High performance SPICE simulator for analog circuit design and analysis',
                'category': 'Analog Circuit Design',
                'popularity': 'Industry Standard',
                'link': 'https://www.analog.com/en/design-center/design-tools-and-calculators/ltspice-simulator.html',
                'license': 'Free',
                'source': 'Analog Devices'
            },
            {
                'name': 'Cadence Virtuoso',
                'description': 'Professional analog and mixed-signal IC design platform',
                'category': 'Analog Circuit Design',
                'popularity': 'Industry Standard',
                'link': 'https://www.cadence.com/en_US/home/<USER>/custom-ic-analog-rf-design/circuit-design/virtuoso-studio.html',
                'license': 'Commercial',
                'source': 'Cadence'
            },
            {
                'name': 'TINA-TI',
                'description': 'SPICE-based analog simulation program for circuit analysis',
                'category': 'Analog Circuit Design',
                'popularity': 'Popular',
                'link': 'https://www.ti.com/tool/TINA-TI',
                'license': 'Free',
                'source': 'Texas Instruments'
            },

            # Digital VLSI Design
            {
                'name': 'Synopsys Design Compiler',
                'description': 'RTL synthesis tool for digital VLSI design',
                'category': 'Digital VLSI Design',
                'popularity': 'Industry Standard',
                'link': 'https://www.synopsys.com/implementation-and-signoff/rtl-synthesis-test/design-compiler-graphical.html',
                'license': 'Commercial',
                'source': 'Synopsys'
            },
            {
                'name': 'Cadence Innovus',
                'description': 'Digital implementation system for place and route',
                'category': 'Digital VLSI Design',
                'popularity': 'Industry Standard',
                'link': 'https://www.cadence.com/en_US/home/<USER>/digital-design-and-signoff/soc-implementation-and-floorplanning/innovus-implementation-system.html',
                'license': 'Commercial',
                'source': 'Cadence'
            },
            {
                'name': 'Yosys',
                'description': 'Open source RTL synthesis framework',
                'category': 'Digital VLSI Design',
                'popularity': 'Growing',
                'link': 'https://yosyshq.net/yosys/',
                'license': 'Open Source',
                'source': 'YosysHQ'
            },

            # Mixed Signal Design
            {
                'name': 'Cadence AMS Designer',
                'description': 'Mixed-signal verification platform',
                'category': 'Mixed Signal Design',
                'popularity': 'Industry Standard',
                'link': 'https://www.cadence.com/en_US/home/<USER>/custom-ic-analog-rf-design/mixed-signal-design/ams-designer.html',
                'license': 'Commercial',
                'source': 'Cadence'
            },
            {
                'name': 'Mentor Questa ADMS',
                'description': 'Advanced mixed-signal simulator',
                'category': 'Mixed Signal Design',
                'popularity': 'Professional',
                'link': 'https://eda.sw.siemens.com/en-US/ic/questa/adms/',
                'license': 'Commercial',
                'source': 'Siemens EDA'
            },

            # Signal Processing
            {
                'name': 'MATLAB Signal Processing Toolbox',
                'description': 'Comprehensive signal processing algorithms and tools',
                'category': 'Signal Processing',
                'popularity': 'Industry Standard',
                'link': 'https://www.mathworks.com/products/signal.html',
                'license': 'Commercial',
                'source': 'MathWorks'
            },
            {
                'name': 'GNU Radio',
                'description': 'Free software development toolkit for software-defined radio',
                'category': 'Signal Processing',
                'popularity': 'Popular',
                'link': 'https://www.gnuradio.org/',
                'license': 'Open Source',
                'source': 'GNU Radio Community'
            },
            {
                'name': 'SciPy Signal',
                'description': 'Python library for signal processing algorithms',
                'category': 'Signal Processing',
                'popularity': 'Very Popular',
                'link': 'https://docs.scipy.org/doc/scipy/reference/signal.html',
                'license': 'Open Source',
                'source': 'SciPy Community'
            }
        ]

        self.tools_data.extend(electronics_tools)
    
    def collect_wireless_comm_tools(self):
        """Collect tools for wireless communication engineers"""
        print("Collecting wireless communication tools...")

        wireless_tools = [
            {
                'name': 'Keysight SystemVue',
                'description': 'Electronic system-level design environment for wireless communications',
                'category': 'Wireless Communication',
                'popularity': 'Industry Standard',
                'link': 'https://www.keysight.com/us/en/products/software/pathwave-design-software/pathwave-system-design.html',
                'license': 'Commercial',
                'source': 'Keysight'
            },
            {
                'name': 'Wireless InSite',
                'description': 'Site-specific radio propagation software for wireless network planning',
                'category': 'Wireless Communication',
                'popularity': 'Professional',
                'link': 'https://www.remcom.com/wireless-insite-em-propagation-software',
                'license': 'Commercial',
                'source': 'Remcom'
            },
            {
                'name': 'SDR-Radio',
                'description': 'Software defined radio receiver application',
                'category': 'Wireless Communication',
                'popularity': 'Popular',
                'link': 'https://www.sdr-radio.com/',
                'license': 'Free',
                'source': 'SDR-Radio'
            }
        ]

        self.tools_data.extend(wireless_tools)

    def collect_embedded_systems_tools(self):
        """Collect tools for embedded systems engineers"""
        print("Collecting embedded systems tools...")

        embedded_tools = [
            {
                'name': 'Keil MDK',
                'description': 'Software development environment for ARM-based microcontrollers',
                'category': 'Embedded Systems',
                'popularity': 'Industry Standard',
                'link': 'https://www2.keil.com/mdk5',
                'license': 'Commercial',
                'source': 'ARM'
            },
            {
                'name': 'Arduino IDE',
                'description': 'Integrated development environment for Arduino boards',
                'category': 'Embedded Systems',
                'popularity': 'Very Popular',
                'link': 'https://www.arduino.cc/en/software',
                'license': 'Open Source',
                'source': 'Arduino'
            },
            {
                'name': 'PlatformIO',
                'description': 'Professional collaborative platform for embedded development',
                'category': 'Embedded Systems',
                'popularity': 'Growing',
                'link': 'https://platformio.org/',
                'license': 'Open Source',
                'source': 'PlatformIO'
            },
            {
                'name': 'STM32CubeIDE',
                'description': 'Integrated development environment for STM32 microcontrollers',
                'category': 'Embedded Systems',
                'popularity': 'Popular',
                'link': 'https://www.st.com/en/development-tools/stm32cubeide.html',
                'license': 'Free',
                'source': 'STMicroelectronics'
            }
        ]

        self.tools_data.extend(embedded_tools)

    def collect_ml_electronics_tools(self):
        """Collect machine learning tools for electronics engineers"""
        print("Collecting ML tools for electronics...")

        ml_tools = [
            {
                'name': 'TensorFlow Lite for Microcontrollers',
                'description': 'Machine learning framework for embedded devices',
                'category': 'ML for Electronics',
                'popularity': 'Growing',
                'link': 'https://www.tensorflow.org/lite/microcontrollers',
                'license': 'Open Source',
                'source': 'Google'
            },
            {
                'name': 'Edge Impulse',
                'description': 'Development platform for machine learning on edge devices',
                'category': 'ML for Electronics',
                'popularity': 'Growing Rapidly',
                'link': 'https://www.edgeimpulse.com/',
                'license': 'Freemium',
                'source': 'Edge Impulse'
            },
            {
                'name': 'MATLAB Deep Learning Toolbox',
                'description': 'Tools for designing and implementing deep neural networks',
                'category': 'ML for Electronics',
                'popularity': 'Professional',
                'link': 'https://www.mathworks.com/products/deep-learning.html',
                'license': 'Commercial',
                'source': 'MathWorks'
            }
        ]

        self.tools_data.extend(ml_tools)

    def collect_all_data(self):
        """Collect data from all sources"""
        print("Starting electronics tools data collection...")
        self.tools_data = []

        # Collect from different domains
        self.collect_electronics_tools()
        self.collect_wireless_comm_tools()
        self.collect_embedded_systems_tools()
        self.collect_ml_electronics_tools()

        # Save to JSON file
        data = {
            'tools': self.tools_data,
            'last_updated': datetime.now().isoformat(),
            'total_count': len(self.tools_data)
        }

        os.makedirs('data', exist_ok=True)
        with open('data/tools.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"Collected {len(self.tools_data)} electronics tools successfully!")
        return data

if __name__ == "__main__":
    collector = ElectronicsToolsCollector()
    collector.collect_all_data()
