# Git Commands to Push Your Code to GitHub

# Step 1: Configure Git (only needed once)
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Step 2: Initialize Git repository in your project folder
git init

# Step 3: Add all files to Git
git add .

# Step 4: Create your first commit
git commit -m "Initial commit - Electronics Engineering Tools Hub"

# Step 5: Connect to your GitHub repository
# Replace YOUR_USERNAME with your actual GitHub username
git remote add origin https://github.com/YOUR_USERNAME/electronics-tools-hub.git

# Step 6: Push your code to GitHub
git branch -M main
git push -u origin main

# If you get authentication errors, you might need to:
# 1. Use a Personal Access Token instead of password
# 2. Or use GitHub Desktop app for easier authentication
